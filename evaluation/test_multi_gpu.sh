#!/bin/bash

# Test script to verify multi-GPU parallel evaluation setup
# Usage: bash test_multi_gpu.sh [NUM_GPUS]

NUM_GPUS=${1:-2}
echo "Testing multi-GPU setup with ${NUM_GPUS} GPUs"

# Function to test GPU availability
test_gpu() {
    local gpu_id=$1
    echo "Testing GPU ${gpu_id}..."
    
    CUDA_VISIBLE_DEVICES=${gpu_id} python3 -c "
import torch
print(f'GPU {gpu_id}: Available = {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'GPU {gpu_id}: Device = {torch.cuda.get_device_name(0)}')
    print(f'GPU {gpu_id}: Memory = {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB')
" &
}

# Test all GPUs in parallel
pids=()
for ((i=0; i<NUM_GPUS; i++)); do
    test_gpu $i
    pids+=($!)
done

# Wait for all tests to complete
for pid in "${pids[@]}"; do
    wait "$pid"
done

echo "Multi-GPU test completed!"

# Test vLLM availability
echo "Testing vLLM installation..."
python3 -c "
try:
    import vllm
    print(f'vLLM version: {vllm.__version__}')
    print('vLLM is available!')
except ImportError as e:
    print(f'vLLM import error: {e}')
"

echo "Setup verification completed!"
