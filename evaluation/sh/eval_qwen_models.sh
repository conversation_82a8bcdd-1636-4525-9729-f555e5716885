#!/bin/bash
set -ex

PROMPT_TYPE=$1
MODEL_NAME_OR_PATH=$2
ENABLE_THINKING=${3:-false}  # Third parameter for enable_thinking, defaults to false

OUTPUT_DIR=${MODEL_NAME_OR_PATH}/math_eval
SPLIT="test"
NUM_TEST_SAMPLE=-1

# Determine if this is a Qwen3 model and set appropriate parameters
if [[ "$MODEL_NAME_OR_PATH" == *"qwen3"* ]] || [[ "$MODEL_NAME_OR_PATH" == *"Qwen3"* ]]; then
    IS_QWEN3=true
    # For Qwen3 thinking mode, use recommended parameters
    if [[ "$ENABLE_THINKING" == "true" ]]; then
        TEMPERATURE=0.6
        TOP_P=0.95
        THINKING_FLAG="--enable_thinking"
        echo "Using Qwen3 thinking mode parameters"
    else
        TEMPERATURE=0.7
        TOP_P=0.8
        THINKING_FLAG=""
        echo "Using Qwen3 non-thinking mode parameters"
    fi
else
    IS_QWEN3=false
    TEMPERATURE=0
    TOP_P=1
    THINKING_FLAG=""
    echo "Using standard parameters for non-Qwen3 model"
fi

echo "Model: ${MODEL_NAME_OR_PATH}"
echo "Prompt Type: ${PROMPT_TYPE}"
echo "Is Qwen3: ${IS_QWEN3}"
echo "Enable Thinking: ${ENABLE_THINKING}"
echo "Temperature: ${TEMPERATURE}"
echo "Top P: ${TOP_P}"

# Target datasets: math, aime24, minerva_math, olympiadbench
DATA_NAME="math,aime24,minerva_math,olympiadbench"
echo "Evaluating datasets: ${DATA_NAME}"

# Disable flash attention to avoid compatibility issues
export DISABLE_FLASH_ATTN=1
# Set HuggingFace cache directory
export HF_HOME=/data_x/junkim100/.cache/huggingface

TOKENIZERS_PARALLELISM=false \
python3 -u math_eval.py \
    --model_name_or_path ${MODEL_NAME_OR_PATH} \
    --data_name ${DATA_NAME} \
    --output_dir ${OUTPUT_DIR} \
    --split ${SPLIT} \
    --prompt_type ${PROMPT_TYPE} \
    --num_test_sample 5 \
    --seed 0 \
    --temperature ${TEMPERATURE} \
    --n_sampling 1 \
    --top_p ${TOP_P} \
    --start 0 \
    --end -1 \
    --save_outputs \
    --overwrite \
    ${THINKING_FLAG}

echo "Evaluation completed for ${MODEL_NAME_OR_PATH}"
