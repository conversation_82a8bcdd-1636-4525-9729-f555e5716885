#!/bin/bash
set -ex

PROMPT_TYPE=$1
MODEL_NAME_OR_PATH=$2
ENABLE_THINKING=${3:-false}  # Third parameter for enable_thinking, defaults to false
NUM_GPUS=${4:-1}  # Fourth parameter for number of GPUs, defaults to 1
USE_VLLM=${5:-true}  # Fifth parameter for using vLLM, defaults to true

OUTPUT_DIR=${MODEL_NAME_OR_PATH}/math_eval
SPLIT="test"
NUM_TEST_SAMPLE=5

# Determine if this is a Qwen3 model and set appropriate parameters
if [[ "$MODEL_NAME_OR_PATH" == *"qwen3"* ]] || [[ "$MODEL_NAME_OR_PATH" == *"Qwen3"* ]]; then
    IS_QWEN3=true
    # For Qwen3 thinking mode, use recommended parameters
    if [[ "$ENABLE_THINKING" == "true" ]]; then
        TEMPERATURE=0.6
        TOP_P=0.95
        THINKING_FLAG="--enable_thinking"
        echo "Using Qwen3 thinking mode parameters"
    else
        TEMPERATURE=0.7
        TOP_P=0.8
        THINKING_FLAG=""
        echo "Using Qwen3 non-thinking mode parameters"
    fi
else
    IS_QWEN3=false
    TEMPERATURE=0
    TOP_P=1
    THINKING_FLAG=""
    echo "Using standard parameters for non-Qwen3 model"
fi

echo "Model: ${MODEL_NAME_OR_PATH}"
echo "Prompt Type: ${PROMPT_TYPE}"
echo "Is Qwen3: ${IS_QWEN3}"
echo "Enable Thinking: ${ENABLE_THINKING}"
echo "Temperature: ${TEMPERATURE}"
echo "Top P: ${TOP_P}"
echo "Number of GPUs: ${NUM_GPUS}"
echo "Use vLLM: ${USE_VLLM}"

# Target datasets: math, aime24, minerva_math, olympiadbench
DATA_NAME="math,aime24,minerva_math,olympiadbench"
echo "Evaluating datasets: ${DATA_NAME}"

# Disable flash attention to avoid compatibility issues
export DISABLE_FLASH_ATTN=1
# Set HuggingFace cache directory
export HF_HOME=/data_x/junkim100/.cache/huggingface

# Set up GPU configuration
if [ "$NUM_GPUS" -gt 1 ]; then
    GPU_LIST=$(seq -s, 0 $((NUM_GPUS-1)))
    export CUDA_VISIBLE_DEVICES="$GPU_LIST"
    echo "Using GPUs: $GPU_LIST"
else
    export CUDA_VISIBLE_DEVICES="0"
    echo "Using single GPU: 0"
fi

# Add vLLM flag if enabled
if [ "$USE_VLLM" = "true" ]; then
    VLLM_FLAG="--use_vllm"
    echo "Using vLLM for faster inference"
else
    VLLM_FLAG=""
    echo "Using HuggingFace transformers"
fi

TOKENIZERS_PARALLELISM=false \
python3 -u math_eval.py \
    --model_name_or_path ${MODEL_NAME_OR_PATH} \
    --data_name ${DATA_NAME} \
    --output_dir ${OUTPUT_DIR} \
    --split ${SPLIT} \
    --prompt_type ${PROMPT_TYPE} \
    --num_test_sample -1 \
    --seed 0 \
    --temperature ${TEMPERATURE} \
    --n_sampling 1 \
    --top_p ${TOP_P} \
    --start 0 \
    --end -1 \
    --save_outputs \
    --overwrite \
    ${VLLM_FLAG} \
    ${THINKING_FLAG}

echo "Evaluation completed for ${MODEL_NAME_OR_PATH}"
