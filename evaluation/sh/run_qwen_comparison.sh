#!/bin/bash
set -ex

# Script to evaluate both Qwen 2.5 7B Instruct and Qwen 3 8B models
# Usage: bash run_qwen_comparison.sh [CUDA_DEVICES]
# Example: bash run_qwen_comparison.sh "0"

CUDA_DEVICES=${1:-"0"}
export CUDA_VISIBLE_DEVICES=${CUDA_DEVICES}

echo "Starting evaluation with CUDA_VISIBLE_DEVICES=${CUDA_DEVICES}"

# # Qwen 2.5 7B Instruct
# echo "=========================================="
# echo "Evaluating Qwen 2.5 7B Instruct"
# echo "=========================================="

# PROMPT_TYPE="qwen25-math-cot"
# MODEL_NAME_OR_PATH="Qwen/Qwen2.5-7B-Instruct"

# bash sh/eval_qwen_models.sh $PROMPT_TYPE $MODEL_NAME_OR_PATH false

# Qwen 3 8B - Non-thinking mode
echo "=========================================="
echo "Evaluating Qwen 3 8B (Non-thinking mode)"
echo "=========================================="

PROMPT_TYPE="qwen3-no-system"
MODEL_NAME_OR_PATH="Qwen/Qwen3-8B"
ENABLE_THINKING=false

bash sh/eval_qwen_models.sh $PROMPT_TYPE $MODEL_NAME_OR_PATH $ENABLE_THINKING