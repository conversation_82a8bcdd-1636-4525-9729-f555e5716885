#!/bin/bash
set -ex

# Script to evaluate both Qwen 2.5 7B Instruct and Qwen 3 8B models in parallel
# Usage: bash run_qwen_comparison.sh [NUM_GPUS] [USE_VLLM] [GPU_UTIL]
# Example: bash run_qwen_comparison.sh 2 true 0.8  # Use 2 GPUs with vLLM, 80% utilization
# Example: bash run_qwen_comparison.sh 1 false 0.9 # Use 1 GPU without vLLM, 90% utilization

NUM_GPUS=${1:-1}
USE_VLLM=${2:-true}
GPU_UTIL=${3:-0.8}

echo "Starting parallel evaluation with ${NUM_GPUS} GPU(s), vLLM: ${USE_VLLM}, GPU utilization: ${GPU_UTIL}"

# Function to run evaluation on specific GPU
run_evaluation() {
    local gpu_id=$1
    local prompt_type=$2
    local model_path=$3
    local enable_thinking=$4
    local use_vllm=$5
    local gpu_util=$6

    echo "Starting evaluation on GPU ${gpu_id}: ${model_path} with ${prompt_type}"

    # Set GPU utilization and run evaluation on specific GPU
    CUDA_VISIBLE_DEVICES=${gpu_id} \
    VLLM_GPU_MEMORY_UTILIZATION=${gpu_util} \
    bash sh/eval_qwen_models.sh "$prompt_type" "$model_path" "$enable_thinking" 1 "$use_vllm" &
}

# Define evaluations to run
evaluations=(
    "0 qwen3-no-system Qwen/Qwen3-8B false"
    "1 qwen3-no-system /data_x/junkim100/projects/finetune/output/Qwen3-8B/LIMO_Qwen3-8B false"
    "2 qwen3-no-system /data_x/junkim100/projects/finetune/output/Qwen3-8B/OpenMathInstruct2_Qwen3-8B false"
    "3 qwen25-math-cot Qwen/Qwen2.5-7B-Instruct false"
    "4 qwen25-math-cot /data_x/junkim100/projects/finetune/output/qwen2.5_7B/LIMO_Qwen2.5_7B false"
    "5 qwen25-math-cot /data_x/junkim100/projects/finetune/output/qwen2.5_7B/OpenMathInstruct2_Qwen2.5_7B false"
)

echo "Running ${#evaluations[@]} evaluations on ${NUM_GPUS} GPU(s)"

# Start evaluations in parallel, cycling through available GPUs
pids=()
eval_count=0
for eval_config in "${evaluations[@]}"; do
    read -r assigned_gpu prompt_type model_path enable_thinking <<< "$eval_config"

    # Use modulo to cycle through available GPUs if we have more evaluations than GPUs
    actual_gpu=$((eval_count % NUM_GPUS))

    run_evaluation "$actual_gpu" "$prompt_type" "$model_path" "$enable_thinking" "$USE_VLLM" "$GPU_UTIL"
    pids+=($!)
    echo "Started evaluation $((eval_count + 1))/6 on GPU ${actual_gpu} (PID: ${!}): ${model_path}"

    eval_count=$((eval_count + 1))

    # Small delay to avoid race conditions
    sleep 5
done

echo "All evaluations started. Waiting for completion..."

# Wait for all evaluations to complete
for pid in "${pids[@]}"; do
    wait "$pid"
    echo "Evaluation with PID $pid completed"
done

echo "All evaluations completed!"