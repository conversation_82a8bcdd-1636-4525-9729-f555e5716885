#!/bin/bash
set -ex

# Script to evaluate both Qwen 2.5 7B Instruct and Qwen 3 8B models in parallel
# Usage: bash run_qwen_comparison.sh [NUM_GPUS] [USE_VLLM] [GPU_UTIL]
# Example: bash run_qwen_comparison.sh 2 true 0.8  # Use 2 GPUs with vLLM, 80% utilization
# Example: bash run_qwen_comparison.sh 1 false 0.9 # Use 1 GPU without vLLM, 90% utilization

NUM_GPUS=${1:-1}
USE_VLLM=${2:-true}
GPU_UTIL=${3:-0.8}

echo "Starting parallel evaluation with ${NUM_GPUS} GPU(s), vLLM: ${USE_VLLM}, GPU utilization: ${GPU_UTIL}"

# Function to run evaluation on specific GPU
run_evaluation() {
    local gpu_id=$1
    local prompt_type=$2
    local model_path=$3
    local enable_thinking=$4
    local use_vllm=$5
    local gpu_util=$6

    echo "Starting evaluation on GPU ${gpu_id}: ${model_path} with ${prompt_type}"

    # Set GPU utilization and run evaluation on specific GPU
    CUDA_VISIBLE_DEVICES=${gpu_id} \
    VLLM_GPU_MEMORY_UTILIZATION=${gpu_util} \
    bash sh/eval_qwen_models.sh "$prompt_type" "$model_path" "$enable_thinking" 1 "$use_vllm" &
}

# Define evaluations to run
evaluations=(
    "0 qwen25-math-cot Qwen/Qwen2.5-7B-Instruct false"
    "1 qwen3-no-system Qwen/Qwen3-8B false"
)

# If we have more GPUs, add more evaluations
if [ "$NUM_GPUS" -ge 3 ]; then
    evaluations+=("2 qwen3-thinking Qwen/Qwen3-8B true")
fi

if [ "$NUM_GPUS" -ge 4 ]; then
    evaluations+=("3 qwen25-math-cot Qwen/Qwen2.5-7B-Instruct false")
fi

echo "Running ${#evaluations[@]} evaluations in parallel on ${NUM_GPUS} GPU(s)"

# Start all evaluations in parallel
pids=()
for eval_config in "${evaluations[@]}"; do
    read -r gpu_id prompt_type model_path enable_thinking <<< "$eval_config"

    # Only run if we have enough GPUs
    if [ "$gpu_id" -lt "$NUM_GPUS" ]; then
        run_evaluation "$gpu_id" "$prompt_type" "$model_path" "$enable_thinking" "$USE_VLLM" "$GPU_UTIL"
        pids+=($!)
        echo "Started evaluation on GPU ${gpu_id} (PID: ${!})"

        # Small delay to avoid race conditions
        sleep 5
    fi
done

echo "All evaluations started. Waiting for completion..."

# Wait for all evaluations to complete
for pid in "${pids[@]}"; do
    wait "$pid"
    echo "Evaluation with PID $pid completed"
done

echo "All evaluations completed!"