#!/bin/bash
set -ex

# Script to evaluate both Qwen 2.5 7B Instruct and Qwen 3 8B models
# Usage: bash run_qwen_comparison.sh [NUM_GPUS] [USE_VLLM]
# Example: bash run_qwen_comparison.sh 2 true  # Use 2 GPUs with vLLM
# Example: bash run_qwen_comparison.sh 1 false # Use 1 GPU without vLLM

NUM_GPUS=${1:-1}
USE_VLLM=${2:-true}

echo "Starting evaluation with ${NUM_GPUS} GPU(s), vLLM: ${USE_VLLM}"

# # Qwen 2.5 7B Instruct
# echo "=========================================="
# echo "Evaluating Qwen 2.5 7B Instruct"
# echo "=========================================="

# PROMPT_TYPE="qwen25-math-cot"
# MODEL_NAME_OR_PATH="Qwen/Qwen2.5-7B-Instruct"

# bash sh/eval_qwen_models.sh $PROMPT_TYPE $MODEL_NAME_OR_PATH false $NUM_GPUS $USE_VLLM

# Qwen 3 8B - Non-thinking mode
echo "=========================================="
echo "Evaluating Qwen 3 8B (Non-thinking mode)"
echo "=========================================="

PROMPT_TYPE="qwen3-no-system"
MODEL_NAME_OR_PATH="Qwen/Qwen3-8B"
ENABLE_THINKING=false

bash sh/eval_qwen_models.sh $PROMPT_TYPE $MODEL_NAME_OR_PATH $ENABLE_THINKING $NUM_GPUS $USE_VLLM