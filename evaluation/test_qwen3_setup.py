#!/usr/bin/env python3
"""
Test script to verify Qwen3 setup and thinking mode functionality.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from parser import extract_qwen3_thinking_content, extract_answer

def test_qwen3_thinking_parser():
    """Test the Qwen3 thinking content parser."""
    print("Testing Qwen3 thinking content parser...")
    
    # Test case 1: Normal thinking output
    test_output_1 = """<think>
Let me solve this step by step.
First, I need to understand what the problem is asking.
The problem asks for the value of x when 2x + 3 = 7.
So I need to solve: 2x + 3 = 7
Subtracting 3 from both sides: 2x = 4
Dividing by 2: x = 2
</think>

To solve this equation, I need to isolate x.

Starting with: 2x + 3 = 7
Subtracting 3 from both sides: 2x = 4
Dividing both sides by 2: x = 2

Therefore, x = 2.

The answer is \\boxed{2}."""
    
    thinking, final = extract_qwen3_thinking_content(test_output_1)
    print(f"Thinking content: {thinking[:100]}...")
    print(f"Final answer: {final[:100]}...")
    
    # Test answer extraction
    extracted_answer = extract_answer(test_output_1, "math")
    print(f"Extracted answer: {extracted_answer}")
    
    # Test case 2: No thinking blocks
    test_output_2 = """To solve 2x + 3 = 7:
Subtract 3: 2x = 4
Divide by 2: x = 2
The answer is \\boxed{2}."""
    
    thinking2, final2 = extract_qwen3_thinking_content(test_output_2)
    print(f"\nNo thinking blocks - Thinking: '{thinking2}'")
    print(f"No thinking blocks - Final: {final2[:50]}...")
    
    extracted_answer2 = extract_answer(test_output_2, "math")
    print(f"Extracted answer: {extracted_answer2}")
    
    print("\nQwen3 thinking parser test completed!")

def test_prompt_templates():
    """Test that our new prompt templates are available."""
    print("\nTesting prompt templates...")
    
    from utils import PROMPT_TEMPLATES
    
    required_templates = ["qwen3-thinking", "qwen3-non-thinking"]
    
    for template in required_templates:
        if template in PROMPT_TEMPLATES:
            print(f"✓ {template} template found")
            input_template, output_template, splitter = PROMPT_TEMPLATES[template]
            print(f"  Input template: {input_template[:50]}...")
        else:
            print(f"✗ {template} template missing")
    
    print("Prompt template test completed!")

def test_model_detection():
    """Test model detection logic."""
    print("\nTesting model detection...")
    
    test_models = [
        "Qwen/Qwen3-8B",
        "Qwen/Qwen2.5-7B-Instruct", 
        "Qwen/qwen3-8b",
        "meta-llama/Llama-2-7b-chat-hf"
    ]
    
    for model in test_models:
        is_qwen3 = "qwen3" in model.lower()
        print(f"Model: {model} -> Is Qwen3: {is_qwen3}")
    
    print("Model detection test completed!")

if __name__ == "__main__":
    print("Running Qwen3 setup tests...\n")
    
    test_qwen3_thinking_parser()
    test_prompt_templates()
    test_model_detection()
    
    print("\n" + "="*50)
    print("All tests completed!")
    print("="*50)
