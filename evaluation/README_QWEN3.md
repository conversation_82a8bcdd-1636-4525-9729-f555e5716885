# Qwen 3 Support for Math Reasoning Evaluation

This document describes the enhanced evaluation framework that supports both Qwen 2.5 and Qwen 3 models, including Qwen 3's unique thinking mode capabilities.

## Overview

The evaluation framework has been extended to support:
- **Qwen 2.5 7B Instruct** with optimized prompt templates
- **Qwen 3 8B** with both thinking and non-thinking modes
- Automatic model detection and parameter optimization
- Proper parsing of Qwen 3's `<think>...</think>` blocks

## Key Features

### 1. Qwen 3 Thinking Mode Support
- **Thinking Mode**: Uses `enable_thinking=True` with recommended parameters (Temperature=0.6, TopP=0.95)
- **Non-Thinking Mode**: Uses `enable_thinking=False` with optimized parameters (Temperature=0.7, TopP=0.8)
- **Automatic Detection**: Automatically detects Qwen 3 models and applies appropriate settings

### 2. Enhanced Prompt Templates
- `qwen3-thinking`: Optimized for Qwen 3 thinking mode
- `qwen3-non-thinking`: Optimized for Qwen 3 non-thinking mode
- `qwen25-math-cot`: Optimized for Qwen 2.5 models

### 3. Target Datasets
The evaluation focuses on these mathematical reasoning datasets:
- `math`: MATH competition dataset
- `aime24`: AIME 2024 competition dataset
- `minerva_math`: Minerva Math dataset
- `olympiadbench`: Olympiad benchmark dataset

## Usage

### Quick Start - Run All Models
```bash
# Run evaluation on both Qwen 2.5 7B Instruct and Qwen 3 8B (both modes)
export CUDA_VISIBLE_DEVICES="0"
bash sh/run_qwen_comparison.sh
```

### Individual Model Evaluation

#### Qwen 2.5 7B Instruct
```bash
export CUDA_VISIBLE_DEVICES="0"
bash sh/eval_qwen_models.sh "qwen25-math-cot" "Qwen/Qwen2.5-7B-Instruct" false
```

#### Qwen 3 8B - Non-Thinking Mode
```bash
export CUDA_VISIBLE_DEVICES="0"
bash sh/eval_qwen_models.sh "qwen3-non-thinking" "Qwen/Qwen3-8B" false
```

#### Qwen 3 8B - Thinking Mode
```bash
export CUDA_VISIBLE_DEVICES="0"
bash sh/eval_qwen_models.sh "qwen3-thinking" "Qwen/Qwen3-8B" true
```

### Manual Evaluation
```bash
python3 -u math_eval.py \
    --model_name_or_path "Qwen/Qwen3-8B" \
    --data_name "math,aime24,minerva_math,olympiadbench" \
    --prompt_type "qwen3-thinking" \
    --enable_thinking \
    --temperature 0.6 \
    --top_p 0.95 \
    --use_vllm \
    --save_outputs \
    --overwrite
```

## Technical Details

### Model Detection
The framework automatically detects Qwen 3 models by checking if "qwen3" is in the model path (case-insensitive).

### Parameter Optimization
- **Qwen 3 Thinking Mode**: Temperature=0.6, TopP=0.95, max_tokens=32768
- **Qwen 3 Non-Thinking Mode**: Temperature=0.7, TopP=0.8
- **Qwen 2.5**: Temperature=0, TopP=1 (greedy decoding)

### Thinking Content Parsing
The framework includes specialized parsing for Qwen 3's thinking outputs:
- Extracts content from `<think>...</think>` blocks
- Separates thinking process from final answer
- Maintains compatibility with existing answer extraction

### Stop Tokens
- Qwen 2: `[151645, 151643]`
- Qwen 3: `[151645, 151643, 151668]` (includes `</think>` token)

## Output Structure

Results are saved in:
```
{MODEL_NAME}/math_eval/
├── math_results.jsonl
├── aime24_results.jsonl
├── minerva_math_results.jsonl
└── olympiadbench_results.jsonl
```

Each result file contains:
- Original question
- Model output (including thinking content for Qwen 3)
- Extracted answer
- Ground truth
- Evaluation metrics

## Testing

Run the test suite to verify setup:
```bash
python test_qwen3_setup.py
```

This tests:
- Qwen 3 thinking content parsing
- Prompt template availability
- Model detection logic
- Answer extraction functionality

## Requirements

Additional dependencies for Qwen 3 support:
```bash
pip install regex latex2sympy2 word2number
```

## Best Practices

1. **For Qwen 3 Thinking Mode**: Always use the recommended sampling parameters (avoid greedy decoding)
2. **Output Length**: Use sufficient max_tokens (32768) for complex reasoning
3. **Evaluation**: Compare both thinking and non-thinking modes to understand model capabilities
4. **Hardware**: Ensure sufficient GPU memory for larger context lengths

## Troubleshooting

### Common Issues
1. **Missing Dependencies**: Install `regex`, `latex2sympy2`, `word2number`
2. **Memory Issues**: Reduce batch size or use model parallelism
3. **Thinking Mode Not Working**: Verify `enable_thinking=True` and correct prompt template

### Debug Mode
Add `--num_test_sample 5` to test with a small subset first:
```bash
bash sh/eval_qwen_models.sh "qwen3-thinking" "Qwen/Qwen3-8B" true --num_test_sample 5
```
