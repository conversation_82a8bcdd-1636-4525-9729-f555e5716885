#!/usr/bin/env python3
"""
Simple test to verify the evaluation setup works.
"""

import os
import sys

# Set environment variables
os.environ["DISABLE_FLASH_ATTN"] = "1"
os.environ["CUDA_VISIBLE_DEVICES"] = "0"

# Test basic imports
try:
    from transformers import AutoTokenizer
    print("✓ Transformers import successful")
except ImportError as e:
    print(f"✗ Transformers import failed: {e}")
    sys.exit(1)

# Test model access
try:
    print("Testing model access...")
    tokenizer = AutoTokenizer.from_pretrained('Qwen/Qwen2.5-7B-Instruct', trust_remote_code=True)
    print("✓ Model tokenizer loaded successfully")
    
    # Test a simple tokenization
    test_text = "What is 2 + 2?"
    tokens = tokenizer.encode(test_text)
    print(f"✓ Tokenization test successful: {len(tokens)} tokens")
    
except Exception as e:
    print(f"✗ Model access failed: {e}")
    print("This might be due to network issues or missing model files.")
    print("The model will be downloaded on first use.")

# Test our custom functions
try:
    from parser import extract_qwen3_thinking_content
    from utils import PROMPT_TEMPLATES
    
    # Test thinking parser
    test_output = "<think>Let me think...</think>The answer is 4."
    thinking, final = extract_qwen3_thinking_content(test_output)
    print(f"✓ Thinking parser test: thinking='{thinking}', final='{final}'")
    
    # Test prompt templates
    if "qwen3-thinking" in PROMPT_TEMPLATES:
        print("✓ Qwen3 prompt templates available")
    else:
        print("✗ Qwen3 prompt templates missing")
        
except Exception as e:
    print(f"✗ Custom function test failed: {e}")

print("\nTest completed!")
