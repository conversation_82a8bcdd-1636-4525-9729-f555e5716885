#!/usr/bin/env python3
"""
Test script to verify model loading works correctly.
"""

import os
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer

# Set environment variables
os.environ["DISABLE_FLASH_ATTN"] = "1"
os.environ["HF_HOME"] = "/data_x/junkim100/.cache/huggingface"
os.environ["CUDA_VISIBLE_DEVICES"] = "0"

def test_model_loading():
    print("Testing model loading...")
    
    try:
        # Try loading with different approaches
        model_name = "Qwen/Qwen2.5-7B-Instruct"
        
        print("Loading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)
        print("✓ Tokenizer loaded successfully")
        
        print("Loading model with device_map='auto'...")
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True,
            low_cpu_mem_usage=True
        )
        print("✓ Model loaded successfully")
        
        # Test a simple generation
        print("Testing generation...")
        test_input = "What is 2 + 2?"
        inputs = tokenizer(test_input, return_tensors="pt")
        
        with torch.no_grad():
            outputs = model.generate(
                inputs.input_ids.to(model.device),
                max_new_tokens=50,
                temperature=0.7,
                do_sample=True,
                pad_token_id=tokenizer.eos_token_id
            )
        
        response = tokenizer.decode(outputs[0], skip_special_tokens=True)
        print(f"✓ Generation test successful: {response}")
        
        return True
        
    except Exception as e:
        print(f"✗ Model loading failed: {e}")
        return False

if __name__ == "__main__":
    success = test_model_loading()
    if success:
        print("\n✓ Model loading test passed!")
    else:
        print("\n✗ Model loading test failed!")
